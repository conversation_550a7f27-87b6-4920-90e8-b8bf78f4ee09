<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>今日巡检情况</span>
        <span class="header-subtitle">INSPECTION OF TODAY</span>
      </div>
    </template>
    <template #content>
      <div class="py-[10px] wh-full pointer-events-auto flex-center gap-x-[10px]">
        <!-- 桥梁 -->
        <div class="flex-1 h-full flex flex-col gap-y-[20px] pt-[10px]">
          <div
            class="w-[120px] h-[35px] flex-center m-auto"
            :style="{
              backgroundImage: `url(${BgPanel1TopBox})`,
              backgroundSize: '100% 100%',
              backgroundRepeat: 'no-repeat'
            }"
          >
            <p class="text-[#fff] text-[14px] font-bold tracking-[5px]">桥梁</p>
          </div>

          <div class="flex-1">
            <CEcharts
              ref="bridgeChartRef"
              :option="chart1Options"
              @onload="onBridgeChartMounted"
            />
          </div>
        </div>

        <!-- 隧道 -->
        <div class="flex-1 h-full flex flex-col gap-y-[20px] pt-[10px]">
          <div
            class="w-[120px] h-[35px] flex-center m-auto"
            :style="{
              backgroundImage: `url(${BgPanel1TopBox})`,
              backgroundSize: '100% 100%',
              backgroundRepeat: 'no-repeat'
            }"
          >
            <p class="text-[#fff] text-[14px] font-bold tracking-[5px]">隧道</p>
          </div>

          <div class="flex-1">
            <CEcharts
              ref="tunnelChartRef"
              :option="chart2Options"
              @onload="onTunnelChartMounted"
            />
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import BgPanel1TopBox from '@/assets/images/bg_panel1_top_box.png'

// 图表实例引用
const bridgeChartRef = ref(null)
const tunnelChartRef = ref(null)
let bridgeChartInstance = null
let tunnelChartInstance = null

// 数据变化定时器
let dataTimer = null

// 生命周期 - 挂载
onMounted(() => {
  // 启动数据变化定时器
  startDataTimer()
})

// 生命周期 - 卸载
onUnmounted(() => {
  if (dataTimer) {
    clearInterval(dataTimer)
  }
})

/*---------------------------------- 图表数据相关 ---------------------------------*/
// 数据 - 图表数据源
const chartData = ref({
  bridge: [
    { label: '已巡检', value: 12 },
    { label: '未巡检', value: 3 }
  ],
  tunnel: [
    { label: '已巡检', value: 23 },
    { label: '未巡检', value: 2 }
  ]
})

// 方法 - 生成随机数据
const generateRandomData = () => {
  const bridgeInspected = Math.floor(Math.random() * 20) + 5
  const bridgeNotInspected = Math.floor(Math.random() * 10) + 1

  const tunnelInspected = Math.floor(Math.random() * 30) + 10
  const tunnelNotInspected = Math.floor(Math.random() * 8) + 1

  chartData.value = {
    bridge: [
      { label: '已巡检', value: bridgeInspected },
      { label: '未巡检', value: bridgeNotInspected }
    ],
    tunnel: [
      { label: '已巡检', value: tunnelInspected },
      { label: '未巡检', value: tunnelNotInspected }
    ]
  }

  // 更新图表配置
  chart1Options.value = createChart1Options()
  chart2Options.value = createChart2Options()
}

// 方法 - 启动数据变化定时器
const startDataTimer = () => {
  // 立即执行一次
  generateRandomData()

  // 每2秒变化一次数据
  dataTimer = setInterval(() => {
    generateRandomData()
  }, 2000)
}

/*---------------------------------- 图表相关配置 ---------------------------------*/
// 方法 - 创建图表1配置项
const createChart1Options = () => {
  return {
    color: ['#00ffaf', '#306fff'],
    tooltip: {
      confine: true
    },
    legend: { // 定制legend
      right: 0,
      top: 'middle',
      orient: 'vertical',
      itemWidth: 4,
      itemHeight: 40,
      itemGap: 20,
      textStyle: {
        color: '#fff',
        fontSize: 13,
        rich: {
          a: {
            padding: [5,5]
          },
          b: {
            padding: [5,5]
          }
        }
      },
      formatter: (category) => {
        // legend -> formatter函数是拿不到数据项，只能到本地变量中获取
        const match = (chartData.value?.bridge || []).find(item => item['label'] === category)

        if (!match) return ''
        return [
          `{a|${category}}\n`,
          `{b|${match['value']}个}`
        ].join('')
      }
    },
    series: [
      {
        name: '巡检情况',
        type: 'pie',
        center: ['35%', '50%'],
        radius: ['60%', '70%'],
        startAngle: 10,
        padAngle: -5,
        minAngle: 10,
        itemStyle: {
          borderRadius: 5
        },
        emphasis: {
          scaleSize: 2
        },
        data: chartData.value.bridge,
        label: {
          show: false,
          position: 'center',
          fontWeight: 'bold',
          formatter: (param) => {
            return [
              `{percentClass|${param.percent}} {unitClass|%}`,
              `{nameClass|${param.name}}`
            ].join('\n')
          },
          rich: {
            percentClass: {
              fontSize: 22,
              color: '#fff',
              lineHeight: 40
            },
            unitClass: {
              fontSize: 12,
              color: '#fff'
            },
            nameClass: {
              fontSize: 14,
              color: '#fff'
            },
            valueClass: {
              fontSize: 14,
              color: '#fff'
            }
          }
        },
        labelLine: {
          show: false
        }
      }
    ]
  }
}

// 数据 - 图表1配置项
const chart1Options = ref(createChart1Options())

// 方法 - 创建图表2配置项
const createChart2Options = () => {
  return {
    color: ['#00ffaf', '#306fff'],
    legend: { // 定制legend
      right: 0,
      top: 'middle',
      orient: 'vertical',
      itemWidth: 4,
      itemHeight: 40,
      itemGap: 20,
      textStyle: {
        color: '#fff',
        fontSize: 13,
        rich: {
          a: {
            padding: [5,5]
          },
          b: {
            padding: [5,5]
          }
        }
      },
      formatter: (category) => {
        // legend -> formatter函数是拿不到数据项，只能到本地变量中获取
        const match = (chartData.value?.tunnel || []).find(item => item['label'] === category)
        if (!match) return ''
        return [
          `{a|${category}}\n`,
          `{b|${match['value']}个}`
        ].join('')
      }
    },
    series: [
      {
        name: '巡检情况',
        type: 'pie',
        center: ['35%', '50%'],
        radius: ['60%', '70%'],
        startAngle: 10,
        padAngle: -5,
        minAngle: 10,
        itemStyle: {
          borderRadius: 5
        },
        emphasis: {
          scaleSize: 2
        },
        data: chartData.value.tunnel,
        label: {
          show: false,
          position: 'center',
          fontWeight: 'bold',
          formatter: (param) => {
            return [
              `{percentClass|${param.percent}} {unitClass|%}`,
              `{nameClass|${param.name}}`
            ].join('\n')
          },
          rich: {
            percentClass: {
              fontSize: 22,
              color: '#fff',
              lineHeight: 40
            },
            unitClass: {
              fontSize: 12,
              color: '#fff'
            },
            nameClass: {
              fontSize: 14,
              color: '#fff'
            },
            valueClass: {
              fontSize: 14,
              color: '#fff'
            }
          }
        },
        labelLine: {
          show: false
        }
      }
    ]
  }
}

// 数据 - 图表2配置项
const chart2Options = ref(createChart2Options())

// 钩子 - 桥梁图表挂载时
const onBridgeChartMounted = (echartsInstance) => {
  bridgeChartInstance = echartsInstance
  echartsInstance.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: 0
  })
}

// 钩子 - 隧道图表挂载时
const onTunnelChartMounted = (echartsInstance) => {
  tunnelChartInstance = echartsInstance
  echartsInstance.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: 0
  })
}
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.inspection-container {
  padding: 20px 0;
}

.sections-row {
  display: flex;
  gap: 40px;
  justify-content: space-between;
}

.section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  padding: 8px 24px;
  background-image: url('@/assets/images/bg_panel1_top_box.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  font-size: 14px;
  margin-bottom: 20px;
  position: relative;
  min-width: 80px;
  text-align: center;
}

.chart-section {
  display: flex;
  align-items: center;
  // gap: 30px;
}

.chart-container {
  position: relative;
  width: 120px;
  height: 120px;

  .progress-chart {
    width: 100%;
    height: 100%;
  }

  .chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .percentage {
      font-size: 20px;
      font-weight: bold;
      color: #00D4FF;
      line-height: 1;
    }

    .label {
      font-size: 12px;
      color: #C5D6E6;
      margin-top: 4px;
    }
  }
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 120px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .stat-line {
      width: 4px;
      height: 40px;
      border-radius: 2px;

      &.green {
        background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
      }

      &.gray {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .stat-label {
        font-size: 14px;
        color: #C5D6E6;
      }

      .stat-value {
        font-size: 16px;
        color: #00D4FF;
        font-weight: bold;
      }
    }
  }
}
</style>
